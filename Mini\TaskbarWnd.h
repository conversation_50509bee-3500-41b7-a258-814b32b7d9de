#pragma once

#define TASKBAR_WINDOW_NAME _T("TaskbarMiniWnd")

class CTaskbarWnd : public CWnd
{
public:
    CTaskbarWnd();
    virtual ~CTaskbarWnd();

    BOOL Create();
    void UpdateDisplay();

protected:
    HWND m_hTaskbar;        // 任务栏句柄
    HWND m_hBar;            // 任务栏二级容器句柄
    HWND m_hMin;            // 最小化窗口句柄
    CRect m_rcTaskbar;      // 任务栏矩形
    CRect m_rcBar;          // 二级容器矩形
    CRect m_rcMin;          // 最小化窗口矩形
    CRect m_rcMinOri;       // 原始最小化窗口矩形
    CRect m_rect;           // 窗口矩形
    
    int m_window_width;     // 窗口宽度
    int m_window_height;    // 窗口高度
    int m_left_space;       // 左边距
    int m_top_space;        // 上边距
    int m_last_width;       // 上次宽度
    int m_last_height;      // 上次高度
    
    bool m_taskbar_on_top_or_bottom;    // 任务栏是否在顶部或底部
    bool m_connot_insert_to_task_bar;   // 是否无法插入任务栏
    bool m_is_secondary_display;        // 是否为副显示器
    
    CFont m_font;           // 字体
    CString m_display_text; // 显示文本
    DWORD m_text_update_count; // 文本更新计数器

    // 消息映射
    DECLARE_MESSAGE_MAP()
    afx_msg int OnCreate(LPCREATESTRUCT lpCreateStruct);
    afx_msg void OnPaint();
    afx_msg void OnDestroy();
    afx_msg void OnTimer(UINT_PTR nIDEvent);

private:
    void InitTaskbarWnd();
    void AdjustWindowPos(bool force_adjust = false);
    void CalculateWindowSize();
    void CheckTaskbarOnTopOrBottom();
    HWND FindTaskbarHandle(bool& is_secondary_display);
    void ResetTaskbarPos();
    void SetTextFont();
    CString GetDisplayText();
};
