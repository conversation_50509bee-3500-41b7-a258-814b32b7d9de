TaskbarMini 使用说明
===================

项目概述
--------
TaskbarMini 是一个基于 MFC 的 Windows 应用程序，演示如何在 Windows 任务栏中嵌入自定义显示窗口。
该项目提取并简化了 TrafficMonitor 项目中的任务栏嵌入功能。

主要功能
--------
1. 在 Windows 任务栏中显示自定义文本内容
2. 提供系统托盘图标，支持右键菜单退出
3. 动态更新显示内容（模拟网速信息）
4. 自动适应任务栏位置变化

编译环境要求
------------
- Windows 10/11 操作系统
- Visual Studio 2017 或更高版本
- Windows SDK 10.0 或更高版本
- MFC 库支持

编译步骤
--------

方法1：使用 Visual Studio IDE
1. 双击打开 TaskbarMini.sln 解决方案文件
2. 在 Visual Studio 中选择 "Release" 配置和 "x86" 平台
3. 点击 "生成" -> "生成解决方案"
4. 编译成功后，可执行文件位于 Release 文件夹中

方法2：使用批处理文件
1. 双击运行 build.bat 文件
2. 脚本会自动查找 Visual Studio 安装路径并编译项目
3. 编译完成后会显示结果

方法3：使用命令行
1. 打开 "开发人员命令提示符"
2. 导航到项目目录
3. 执行命令：msbuild TaskbarMini.vcxproj /p:Configuration=Release /p:Platform=Win32

运行说明
--------
1. 运行编译生成的 TaskbarMini.exe
2. 程序启动后会自动隐藏主窗口并最小化到系统托盘
3. 在 Windows 任务栏中会出现一个黑色的小窗口
4. 该窗口会显示模拟的网速信息，每秒更新一次
5. 右键点击系统托盘图标可以选择退出程序

技术原理
--------
1. 任务栏嵌入：
   - 使用 FindWindow 查找任务栏窗口句柄 (Shell_TrayWnd)
   - 查找任务栏的子窗口 (ReBarWindow32, MSTaskSwWClass)
   - 使用 SetParent 将自定义窗口设置为任务栏的子窗口
   - 调整最小化窗口区域为自定义窗口腾出空间

2. 位置自适应：
   - 检测任务栏位置（顶部/底部 vs 左侧/右侧）
   - 根据任务栏方向调整窗口布局
   - 定期检查并调整窗口位置

3. 系统托盘：
   - 使用 Shell_NotifyIcon API 创建托盘图标
   - 处理托盘图标的右键菜单消息
   - 提供退出功能

注意事项
--------
1. 管理员权限：在某些系统上可能需要管理员权限才能成功嵌入任务栏
2. 兼容性：在不同版本的 Windows 上表现可能略有差异
3. 任务栏主题：某些第三方任务栏主题可能影响显示效果
4. 多显示器：当前版本主要支持主显示器的任务栏

故障排除
--------
1. 编译错误：
   - 确保已安装 Visual Studio 和 MFC 库
   - 检查 Windows SDK 版本是否正确
   - 确认项目文件路径中没有中文字符

2. 无法嵌入任务栏：
   - 尝试以管理员权限运行程序
   - 检查任务栏是否被锁定
   - 确认没有其他程序占用任务栏空间

3. 显示位置不正确：
   - 重启程序让其重新检测任务栏位置
   - 检查任务栏设置是否为自动隐藏

4. 托盘图标不显示：
   - 检查系统托盘设置
   - 确认程序没有被防火墙阻止

扩展开发
--------
如果需要扩展功能，可以考虑以下方向：

1. 真实数据源：
   - 集成 Windows Performance Toolkit API
   - 获取真实的网络流量数据
   - 添加 CPU、内存使用率显示

2. 界面美化：
   - 支持皮肤和主题
   - 添加图标和图形显示
   - 支持透明度和特效

3. 配置选项：
   - 添加设置界面
   - 支持自定义显示内容
   - 保存用户配置

4. 多显示器支持：
   - 检测多显示器环境
   - 支持在副显示器任务栏显示

文件结构
--------
TaskbarMini/
├── TaskbarMini.cpp/h          # 应用程序入口
├── TaskbarMiniDlg.cpp/h       # 主对话框类
├── TaskbarWnd.cpp/h           # 任务栏窗口类
├── stdafx.cpp/h               # 预编译头文件
├── targetver.h                # 目标版本定义
├── resource.h                 # 资源ID定义
├── TaskbarMini.rc             # 资源文件
├── TaskbarMini.vcxproj        # 项目文件
├── TaskbarMini.sln            # 解决方案文件
├── build.bat                  # 编译脚本
├── README.md                  # 项目说明
├── 使用说明.txt               # 详细使用说明
├── 图标说明.txt               # 图标配置说明
└── res/                       # 资源文件夹
    └── TaskbarMini.rc2        # 附加资源文件

联系信息
--------
如有问题或建议，请参考 README.md 文件或查看源代码注释。
