图标文件说明
==============

当前项目配置为使用系统默认图标，可以正常编译和运行。

如果需要自定义图标，请按以下步骤操作：

1. 准备图标文件
   - 创建或获取一个 32x32 像素的 .ico 图标文件
   - 将其命名为 TaskbarMini.ico
   - 放置在 res 文件夹中

2. 修改资源文件
   - 打开 TaskbarMini.rc 文件
   - 找到被注释的图标资源行：
     // IDR_MAINFRAME           ICON                    "res\\TaskbarMini.ico"
   - 取消注释该行

3. 修改项目文件
   - 打开 TaskbarMini.vcxproj 文件
   - 找到被注释的图标项目：
     <!-- 图标文件暂时注释掉
     <ItemGroup>
       <Image Include="res\TaskbarMini.ico" />
     </ItemGroup>
     -->
   - 取消注释该部分

4. 修改代码
   - 打开 TaskbarMiniDlg.cpp 文件
   - 找到图标加载代码：
     m_hIcon = LoadIcon(NULL, IDI_APPLICATION);
   - 改为：
     m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);

完成以上步骤后重新编译即可使用自定义图标。
