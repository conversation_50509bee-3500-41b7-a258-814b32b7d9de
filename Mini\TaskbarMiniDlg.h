#pragma once
#include "TaskbarWnd.h"

// CTaskbarMiniDlg 对话框
class CTaskbarMiniDlg : public CDialogEx
{
// 构造
public:
	CTaskbarMiniDlg(CWnd* pParent = NULL);	// 标准构造函数

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_TASKBARMINI_DIALOG };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV 支持

// 实现
protected:
	HICON m_hIcon;
	CTaskbarWnd* m_pTaskbarWnd;
	NOTIFYICONDATA m_nid;

	// 生成的消息映射函数
	virtual BOOL OnInitDialog();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	afx_msg void OnDestroy();
	afx_msg LRESULT OnTrayIcon(WPARAM wParam, LPARAM lParam);
	afx_msg void OnMenuExit();
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	DECLARE_MESSAGE_MAP()

private:
	void CreateTrayIcon();
	void RemoveTrayIcon();
	void ShowTrayMenu();
};
