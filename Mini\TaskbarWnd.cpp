#include "stdafx.h"
#include "TaskbarWnd.h"

#define TIMER_ADJUST_POS 1

BEGIN_MESSAGE_MAP(CTaskbarWnd, CWnd)
    ON_WM_CREATE()
    ON_WM_PAINT()
    ON_WM_DESTROY()
    ON_WM_TIMER()
END_MESSAGE_MAP()

CTaskbarWnd::CTaskbarWnd()
    : m_hTaskbar(NULL)
    , m_hBar(NULL)
    , m_hMin(NULL)
    , m_window_width(120)
    , m_window_height(24)
    , m_left_space(0)
    , m_top_space(0)
    , m_last_width(0)
    , m_last_height(0)
    , m_taskbar_on_top_or_bottom(true)
    , m_connot_insert_to_task_bar(false)
    , m_is_secondary_display(false)
    , m_text_update_count(0)
{
    m_display_text = _T("TaskbarMini");
}

CTaskbarWnd::~CTaskbarWnd()
{
    ResetTaskbarPos();
}

BOOL CTaskbarWnd::Create()
{
    // 注册窗口类
    CString className = AfxRegisterWndClass(CS_HREDRAW | CS_VREDRAW, 
        ::LoadCursor(NULL, IDC_ARROW), 
        (HBRUSH)(COLOR_WINDOW + 1), 
        NULL);

    // 创建窗口
    if (!CreateEx(WS_EX_TOOLWINDOW, className, TASKBAR_WINDOW_NAME,
        WS_POPUP | WS_VISIBLE, 0, 0, m_window_width, m_window_height, NULL, NULL))
    {
        return FALSE;
    }

    return TRUE;
}

int CTaskbarWnd::OnCreate(LPCREATESTRUCT lpCreateStruct)
{
    if (CWnd::OnCreate(lpCreateStruct) == -1)
        return -1;

    // 查找任务栏句柄
    m_hTaskbar = FindTaskbarHandle(m_is_secondary_display);
    if (m_hTaskbar == NULL)
    {
        return -1;
    }

    // 初始化任务栏窗口
    InitTaskbarWnd();

    // 设置字体
    SetTextFont();

    // 检查任务栏位置
    CheckTaskbarOnTopOrBottom();

    // 计算窗口大小
    CalculateWindowSize();

    // 尝试嵌入任务栏
    m_connot_insert_to_task_bar = !(::SetParent(m_hWnd, m_hBar));

    // 调整窗口位置
    AdjustWindowPos(true);

    // 设置定时器定期调整位置
    SetTimer(TIMER_ADJUST_POS, 5000, NULL);

    return 0;
}

void CTaskbarWnd::OnPaint()
{
    CPaintDC dc(this);
    
    CRect rect;
    GetClientRect(&rect);

    // 设置背景色
    dc.FillSolidRect(&rect, RGB(0, 0, 0));

    // 设置字体和文本颜色
    dc.SelectObject(&m_font);
    dc.SetTextColor(RGB(255, 255, 255));
    dc.SetBkMode(TRANSPARENT);

    // 绘制文本
    dc.DrawText(m_display_text, &rect, DT_CENTER | DT_VCENTER | DT_SINGLELINE);
}

void CTaskbarWnd::OnDestroy()
{
    KillTimer(TIMER_ADJUST_POS);
    ResetTaskbarPos();
    CWnd::OnDestroy();
}

void CTaskbarWnd::OnTimer(UINT_PTR nIDEvent)
{
    if (nIDEvent == TIMER_ADJUST_POS)
    {
        // 定期检查并调整窗口位置
        AdjustWindowPos();
    }
    CWnd::OnTimer(nIDEvent);
}

void CTaskbarWnd::UpdateDisplay()
{
    // 更新显示文本
    m_display_text = GetDisplayText();
    
    // 重绘窗口
    if (IsWindow(m_hWnd))
    {
        Invalidate();
    }
}

void CTaskbarWnd::InitTaskbarWnd()
{
    // 查找任务栏的子窗口
    m_hBar = ::FindWindowEx(m_hTaskbar, 0, L"ReBarWindow32", NULL);
    if (m_hBar == NULL)
        m_hBar = ::FindWindowEx(m_hTaskbar, nullptr, L"WorkerW", NULL);
    
    m_hMin = ::FindWindowEx(m_hBar, 0, L"MSTaskSwWClass", NULL);
    if (m_hMin == NULL)
        m_hMin = ::FindWindowEx(m_hBar, 0, L"MSTaskListWClass", NULL);

    if (m_hMin != NULL)
    {
        ::GetWindowRect(m_hMin, m_rcMin);
        ::GetWindowRect(m_hBar, m_rcBar);
        
        m_left_space = m_rcMin.left - m_rcBar.left;
        m_top_space = m_rcMin.top - m_rcBar.top;
    }
}

void CTaskbarWnd::AdjustWindowPos(bool force_adjust)
{
    if (!IsWindow(m_hWnd))
        return;

    ::GetWindowRect(m_hTaskbar, m_rcTaskbar);
    ::GetWindowRect(m_hMin, m_rcMin);
    ::GetWindowRect(m_hBar, m_rcBar);

    CheckTaskbarOnTopOrBottom();

    if (m_taskbar_on_top_or_bottom)
    {
        // 任务栏在顶部或底部
        m_rect.right = m_rect.left + m_window_width;
        m_rect.bottom = m_rect.top + m_window_height;

        if (force_adjust || m_rcMin.Width() != m_last_width)
        {
            m_rcMinOri = m_rcMin;
            m_left_space = m_rcMin.left - m_rcBar.left;
            m_last_width = m_rcMin.Width() - m_rect.Width();

            // 调整最小化窗口位置，为我们的窗口腾出空间
            ::MoveWindow(m_hMin, m_left_space, 0, 
                m_rcMin.Width() - m_rect.Width(), m_rcMin.Height(), TRUE);
            
            // 设置我们窗口的位置
            m_rect.MoveToX(m_left_space + m_rcMin.Width() - m_rect.Width() + 2);
            m_rect.MoveToY((m_rcBar.Height() - m_rect.Height()) / 2);
            
            MoveWindow(m_rect.left, m_rect.top, m_rect.Width(), m_rect.Height(), TRUE);
        }
    }
    else
    {
        // 任务栏在左侧或右侧
        if (force_adjust || m_rcMin.Height() != m_last_height)
        {
            m_rcMinOri = m_rcMin;
            m_top_space = m_rcMin.top - m_rcBar.top;
            m_last_height = m_rcMin.Height() - m_rect.Height();

            ::MoveWindow(m_hMin, 0, m_top_space, 
                m_rcMin.Width(), m_rcMin.Height() - m_rect.Height(), TRUE);
            
            m_rect.MoveToY(m_top_space + m_rcMin.Height() - m_rect.Height() + 2);
            m_rect.MoveToX((m_rcMin.Width() - m_window_width) / 2);
            
            MoveWindow(m_rect.left, m_rect.top, m_rect.Width(), m_rect.Height(), TRUE);
        }
    }
}

void CTaskbarWnd::CalculateWindowSize()
{
    // 简单的固定大小，实际项目中可以根据文本内容动态计算
    m_window_width = 120;
    m_window_height = 24;
    
    m_rect.right = m_rect.left + m_window_width;
    m_rect.bottom = m_rect.top + m_window_height;
}

void CTaskbarWnd::CheckTaskbarOnTopOrBottom()
{
    ::GetWindowRect(m_hTaskbar, m_rcTaskbar);
    
    // 简单判断：如果任务栏宽度大于高度，则认为在顶部或底部
    m_taskbar_on_top_or_bottom = (m_rcTaskbar.Width() > m_rcTaskbar.Height());
}

HWND CTaskbarWnd::FindTaskbarHandle(bool& is_secondary_display)
{
    is_secondary_display = false;
    HWND hTaskbar = ::FindWindow(_T("Shell_TrayWnd"), NULL);
    return hTaskbar;
}

void CTaskbarWnd::ResetTaskbarPos()
{
    // 程序关闭时恢复最小化窗口的原始位置
    if (!m_rcMinOri.IsRectEmpty() && m_hMin != NULL)
    {
        if (m_taskbar_on_top_or_bottom)
            ::MoveWindow(m_hMin, m_left_space, 0, m_rcMinOri.Width(), m_rcMinOri.Height(), TRUE);
        else
            ::MoveWindow(m_hMin, 0, m_top_space, m_rcMinOri.Width(), m_rcMinOri.Height(), TRUE);
    }
}

void CTaskbarWnd::SetTextFont()
{
    // 创建字体
    LOGFONT lf = { 0 };
    lf.lfHeight = -12;
    lf.lfWeight = FW_NORMAL;
    lf.lfCharSet = DEFAULT_CHARSET;
    lf.lfOutPrecision = OUT_DEFAULT_PRECIS;
    lf.lfClipPrecision = CLIP_DEFAULT_PRECIS;
    lf.lfQuality = DEFAULT_QUALITY;
    lf.lfPitchAndFamily = DEFAULT_PITCH | FF_DONTCARE;
    _tcscpy_s(lf.lfFaceName, _T("微软雅黑"));
    
    m_font.CreateFontIndirect(&lf);
}

CString CTaskbarWnd::GetDisplayText()
{
    // 生成动态文本内容
    CString text;
    m_text_update_count++;
    
    // 模拟网速显示
    int upload_speed = (m_text_update_count * 13) % 1000;
    int download_speed = (m_text_update_count * 17) % 2000;
    
    text.Format(_T("↑%dKB/s ↓%dKB/s"), upload_speed, download_speed);
    
    return text;
}
