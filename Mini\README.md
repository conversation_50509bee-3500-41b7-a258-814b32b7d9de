# TaskbarMini - 任务栏显示演示程序

这是一个简化的MFC项目，演示如何在Windows任务栏中嵌入显示窗口。

## 功能特性

1. **任务栏嵌入显示**: 在Windows任务栏中显示自定义文本内容
2. **系统托盘图标**: 提供托盘图标，支持右键菜单退出
3. **动态内容更新**: 模拟显示动态变化的网速信息
4. **自动位置调整**: 自动适应任务栏位置变化

## 核心技术

### 任务栏嵌入原理
- 通过 `FindWindow` 查找任务栏窗口句柄
- 使用 `SetParent` 将自定义窗口设置为任务栏的子窗口
- 调整最小化窗口区域为自定义窗口腾出空间
- 定期检查并调整窗口位置

### 关键代码文件
- `TaskbarWnd.cpp/h`: 任务栏窗口实现，包含嵌入逻辑
- `TaskbarMiniDlg.cpp/h`: 主对话框，管理托盘图标和任务栏窗口
- `TaskbarMini.cpp/h`: 应用程序入口

## 编译要求

- Visual Studio 2017 或更高版本
- Windows SDK
- MFC 库支持

## 编译步骤

### 方法1: 使用批处理文件
```bash
build.bat
```

### 方法2: 使用Visual Studio
1. 用Visual Studio打开 `TaskbarMini.vcxproj`
2. 选择Release配置
3. 生成解决方案

### 方法3: 使用命令行
```bash
# 设置VS环境变量
"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat"

# 编译项目
msbuild TaskbarMini.vcxproj /p:Configuration=Release /p:Platform=Win32
```

## 使用说明

1. 运行编译生成的 `TaskbarMini.exe`
2. 程序会自动最小化到系统托盘
3. 在任务栏中会出现一个黑色窗口，显示模拟的网速信息
4. 右键点击托盘图标可以退出程序

## 注意事项

1. **图标文件**: 需要在 `res` 文件夹中放置真正的 `TaskbarMini.ico` 图标文件
2. **管理员权限**: 某些情况下可能需要管理员权限才能成功嵌入任务栏
3. **兼容性**: 在不同版本的Windows上表现可能略有差异
4. **任务栏位置**: 支持任务栏在屏幕四个边的情况

## 核心代码说明

### 任务栏嵌入核心逻辑
```cpp
// 查找任务栏窗口
m_hTaskbar = FindWindow(_T("Shell_TrayWnd"), NULL);

// 查找任务栏子窗口
m_hBar = FindWindowEx(m_hTaskbar, 0, L"ReBarWindow32", NULL);
m_hMin = FindWindowEx(m_hBar, 0, L"MSTaskSwWClass", NULL);

// 设置为任务栏子窗口
SetParent(m_hWnd, m_hBar);

// 调整最小化窗口位置为自定义窗口腾出空间
MoveWindow(m_hMin, m_left_space, 0, 
    m_rcMin.Width() - m_rect.Width(), m_rcMin.Height(), TRUE);
```

### 动态文本更新
```cpp
CString CTaskbarWnd::GetDisplayText()
{
    CString text;
    m_text_update_count++;
    
    // 模拟网速显示
    int upload_speed = (m_text_update_count * 13) % 1000;
    int download_speed = (m_text_update_count * 17) % 2000;
    
    text.Format(_T("↑%dKB/s ↓%dKB/s"), upload_speed, download_speed);
    
    return text;
}
```

## 扩展建议

1. **真实数据**: 可以集成真实的网络监控API获取实际网速数据
2. **皮肤系统**: 添加皮肤支持，允许自定义外观
3. **配置选项**: 添加配置界面，允许用户自定义显示内容
4. **多显示器支持**: 支持多显示器环境下的任务栏检测

## 故障排除

1. **无法嵌入任务栏**: 尝试以管理员权限运行
2. **显示位置不正确**: 检查任务栏位置检测逻辑
3. **编译错误**: 确保已安装MFC库和Windows SDK

## 许可证

本项目仅供学习和演示使用。
