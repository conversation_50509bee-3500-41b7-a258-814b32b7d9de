@echo off
echo 编译 TaskbarMini 项目...

REM 查找 Visual Studio 安装路径
set VS_PATH=""
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat" (
    set VS_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\Common7\Tools\VsDevCmd.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat" (
    set VS_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\Common7\Tools\VsDevCmd.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\Common7\Tools\VsDevCmd.bat" (
    set VS_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\Common7\Tools\VsDevCmd.bat"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\Common7\Tools\VsDevCmd.bat" (
    set VS_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\Common7\Tools\VsDevCmd.bat"
)

if %VS_PATH%=="" (
    echo 错误: 找不到 Visual Studio 安装路径
    echo 请确保已安装 Visual Studio 2017 或 2019
    pause
    exit /b 1
)

REM 设置编译环境
call %VS_PATH%

REM 编译项目
msbuild TaskbarMini.vcxproj /p:Configuration=Release /p:Platform=Win32

if %ERRORLEVEL%==0 (
    echo 编译成功！
    echo 可执行文件位置: Release\TaskbarMini.exe
) else (
    echo 编译失败！
)

pause
