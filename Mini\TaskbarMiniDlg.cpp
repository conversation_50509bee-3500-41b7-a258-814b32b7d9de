#include "stdafx.h"
#include "TaskbarMini.h"
#include "TaskbarMiniDlg.h"
#include "afxdialogex.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// 用于应用程序"关于"菜单项的 CAboutDlg 对话框

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// 对话框数据
#ifdef AFX_DESIGN_TIME
	enum { IDD = IDD_ABOUTBOX };
#endif

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持

// 实现
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialogEx(IDD_ABOUTBOX)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()

// CTaskbarMiniDlg 对话框

#define WM_TRAYICON (WM_USER + 1)
#define TIMER_UPDATE 1

CTaskbarMiniDlg::CTaskbarMiniDlg(CWnd* pParent /*=NULL*/)
	: CDialogEx(IDD_TASKBARMINI_DIALOG, pParent)
	, m_pTaskbarWnd(nullptr)
{
	// 使用系统默认应用程序图标
	m_hIcon = LoadIcon(NULL, IDI_APPLICATION);
}

void CTaskbarMiniDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CTaskbarMiniDlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_WM_DESTROY()
	ON_MESSAGE(WM_TRAYICON, &CTaskbarMiniDlg::OnTrayIcon)
	ON_COMMAND(ID_MENU_EXIT, &CTaskbarMiniDlg::OnMenuExit)
	ON_WM_TIMER()
END_MESSAGE_MAP()

// CTaskbarMiniDlg 消息处理程序

BOOL CTaskbarMiniDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// 将"关于..."菜单项添加到系统菜单中。

	// IDM_ABOUTBOX 必须在系统命令范围内。
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != NULL)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	// 设置此对话框的图标。  当应用程序主窗口不是对话框时，框架将自动
	//  执行此操作
	SetIcon(m_hIcon, TRUE);			// 设置大图标
	SetIcon(m_hIcon, FALSE);		// 设置小图标

	// 隐藏主对话框
	ShowWindow(SW_HIDE);

	// 创建系统托盘图标
	CreateTrayIcon();

	// 创建任务栏窗口
	m_pTaskbarWnd = new CTaskbarWnd();
	if (m_pTaskbarWnd->Create())
	{
		// 启动定时器更新显示内容
		SetTimer(TIMER_UPDATE, 1000, NULL);
	}

	return TRUE;  // 除非将焦点设置到控件，否则返回 TRUE
}

void CTaskbarMiniDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

// 如果向对话框添加最小化按钮，则需要下面的代码
//  来绘制该图标。  对于使用文档/视图模型的 MFC 应用程序，
//  这将由框架自动完成。

void CTaskbarMiniDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // 用于绘制的设备上下文

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// 使图标在工作区矩形中居中
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// 绘制图标
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

//当用户拖动最小化窗口时系统调用此函数取得光标
//显示。
HCURSOR CTaskbarMiniDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}

void CTaskbarMiniDlg::OnDestroy()
{
	// 清理资源
	if (m_pTaskbarWnd)
	{
		delete m_pTaskbarWnd;
		m_pTaskbarWnd = nullptr;
	}

	RemoveTrayIcon();
	KillTimer(TIMER_UPDATE);

	CDialogEx::OnDestroy();
}

void CTaskbarMiniDlg::CreateTrayIcon()
{
	memset(&m_nid, 0, sizeof(m_nid));
	m_nid.cbSize = sizeof(m_nid);
	m_nid.hWnd = m_hWnd;
	m_nid.uID = 1;
	m_nid.uFlags = NIF_ICON | NIF_MESSAGE | NIF_TIP;
	m_nid.uCallbackMessage = WM_TRAYICON;
	m_nid.hIcon = m_hIcon;
	_tcscpy_s(m_nid.szTip, _T("TaskbarMini - 任务栏显示演示"));

	Shell_NotifyIcon(NIM_ADD, &m_nid);
}

void CTaskbarMiniDlg::RemoveTrayIcon()
{
	Shell_NotifyIcon(NIM_DELETE, &m_nid);
}

LRESULT CTaskbarMiniDlg::OnTrayIcon(WPARAM wParam, LPARAM lParam)
{
	if (lParam == WM_RBUTTONUP)
	{
		ShowTrayMenu();
	}
	return 0;
}

void CTaskbarMiniDlg::ShowTrayMenu()
{
	CMenu menu;
	menu.CreatePopupMenu();
	menu.AppendMenu(MF_STRING, ID_MENU_EXIT, _T("退出"));

	CPoint pt;
	GetCursorPos(&pt);
	SetForegroundWindow();
	menu.TrackPopupMenu(TPM_RIGHTBUTTON, pt.x, pt.y, this);
	menu.DestroyMenu();
}

void CTaskbarMiniDlg::OnMenuExit()
{
	EndDialog(IDCANCEL);
}

void CTaskbarMiniDlg::OnTimer(UINT_PTR nIDEvent)
{
	if (nIDEvent == TIMER_UPDATE && m_pTaskbarWnd)
	{
		// 更新任务栏窗口显示的内容
		m_pTaskbarWnd->UpdateDisplay();
	}

	CDialogEx::OnTimer(nIDEvent);
}
